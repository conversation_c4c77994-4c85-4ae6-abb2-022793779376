{"format_version": "1.21.70", "minecraft:entity": {"description": {"identifier": "ptd_dbb:void_hydra", "is_spawnable": true, "is_summonable": true, "properties": {"ptd_dbb:spawning": {"type": "bool", "client_sync": true, "default": true}, "ptd_dbb:dead": {"type": "bool", "client_sync": true, "default": false}, "ptd_dbb:death_timer": {"type": "int", "client_sync": true, "range": [0, 150], "default": 0}, "ptd_dbb:attack": {"type": "enum", "client_sync": true, "default": "none", "values": ["none", "right_atomic_cross", "right_atomic", "right_vacuum", "right_summon", "mid_atomic", "mid_meteor", "mid_singularity", "left_atomic_cross", "left_atomic", "left_railgun", "left_missile", "left_shout"]}, "ptd_dbb:attack_timer": {"type": "int", "client_sync": true, "range": [0, 300], "default": 0}, "ptd_dbb:attack_cooldown": {"type": "int", "client_sync": false, "range": [0, 100], "default": 0}, "ptd_dbb:cooling_down": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"ptd_dbb:spawning": {"minecraft:is_collidable": {}, "minecraft:timer": {"time": 14, "looping": false, "time_down_event": {"event": "ptd_dbb:on_spawn", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}}, "ptd_dbb:default": {"minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 10}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:dead", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:spawning", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "void_hydra"}}, "deals_damage": "no"}]}}, "ptd_dbb:targeting": {"minecraft:environment_sensor": {"triggers": [{"filters": [{"test": "target_distance", "subject": "other", "operator": "<=", "value": 32}, {"test": "enum_property", "subject": "self", "domain": "ptd_dbb:attack", "operator": "==", "value": "none"}, {"test": "bool_property", "subject": "self", "domain": "ptd_dbb:cooling_down", "operator": "==", "value": false}], "event": "ptd_dbb:attack"}]}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "must_see": true, "attack_interval": 1, "reselect_targets": true, "must_see_forget_duration": 0, "reevaluate_description": true, "entity_types": [{"priority": 0, "max_dist": 64, "filters": {"test": "is_family", "subject": "other", "value": "player"}}, {"priority": 1, "max_dist": 64, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "boss"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "void_hydra"}, {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "other", "operator": "==", "value": false}, {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "other", "operator": "==", "value": false}]}}, {"priority": 2, "max_dist": 64, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "minion"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "void_hydra"}, {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "other", "operator": "==", "value": false}, {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "other", "operator": "==", "value": false}]}}]}}, "ptd_dbb:melee": {"minecraft:attack": {"damage": 0}, "minecraft:movement": {"value": 0.3}, "minecraft:movement.hover": {}, "minecraft:flying_speed": {"value": 0.15}, "minecraft:navigation.hover": {"can_path_over_water": true, "can_sink": false, "can_pass_doors": false, "can_path_from_air": true, "avoid_water": true, "avoid_damage_blocks": true, "avoid_sun": false}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.look_at_player": {"priority": 9, "look_distance": 32, "probability": 0.8}, "minecraft:behavior.random_look_around": {"priority": 10}, "minecraft:behavior.random_hover": {"priority": 12, "xz_dist": 8, "y_dist": 8, "y_offset": -1, "interval": 1, "hover_height": [8, 24]}}, "ptd_dbb:dead": {"minecraft:timer": {"time": 20, "looping": false, "time_down_event": {"event": "ptd_dbb:despawn", "target": "self"}}, "minecraft:is_collidable": {}, "minecraft:movement": {"value": 0}, "minecraft:navigation.walk": {"is_amphibious": false, "can_pass_doors": false, "can_walk": false, "can_swim": false, "can_sink": false, "avoid_sun": false}, "minecraft:behavior.random_look_around": {"priority": 999999}, "minecraft:behavior.random_stroll": {"priority": 999999}, "minecraft:body_rotation_blocked": {}}, "ptd_dbb:despawn": {"minecraft:instant_despawn": {}}, "ptd_dbb:cooldown": {"minecraft:timer": {"time": 1.0, "looping": false, "time_down_event": {"event": "ptd_dbb:cooldown_complete", "target": "self"}}}}, "events": {"minecraft:entity_spawned": {"sequence": [{"queue_command": {"command": "tp @s ~ ~8 ~"}}, {"add": {"component_groups": ["ptd_dbb:spawning"]}}]}, "ptd_dbb:on_spawn": {"sequence": [{"remove": {"component_groups": ["ptd_dbb:spawning"]}, "set_property": {"ptd_dbb:spawning": false}}, {"add": {"component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]}}]}, "ptd_dbb:dead": {"sequence": [{"set_property": {"ptd_dbb:dead": true}}, {"remove": {"component_groups": ["ptd_dbb:targeting", "ptd_dbb:melee"]}}, {"add": {"component_groups": ["ptd_dbb:dead", "ptd_dbb:default"]}}, {"queue_command": {"command": ["scriptevent ptd_dbb:void_hydra_death"]}}]}, "ptd_dbb:despawn": {"add": {"component_groups": ["ptd_dbb:despawn"]}}, "ptd_dbb:attack": {}, "ptd_dbb:right_atomic_cross_attack": {"set_property": {"ptd_dbb:attack": "right_atomic_cross"}}, "ptd_dbb:right_atomic_attack": {"set_property": {"ptd_dbb:attack": "right_atomic"}}, "ptd_dbb:right_vacuum_attack": {"set_property": {"ptd_dbb:attack": "right_vacuum"}}, "ptd_dbb:right_summon_attack": {"set_property": {"ptd_dbb:attack": "right_summon"}}, "ptd_dbb:mid_atomic_attack": {"set_property": {"ptd_dbb:attack": "mid_atomic"}}, "ptd_dbb:mid_meteor_attack": {"set_property": {"ptd_dbb:attack": "mid_meteor"}}, "ptd_dbb:mid_singularity_attack": {"set_property": {"ptd_dbb:attack": "mid_singularity"}}, "ptd_dbb:left_atomic_cross_attack": {"set_property": {"ptd_dbb:attack": "left_atomic_cross"}}, "ptd_dbb:left_atomic_attack": {"set_property": {"ptd_dbb:attack": "left_atomic"}}, "ptd_dbb:left_railgun_attack": {"set_property": {"ptd_dbb:attack": "left_railgun"}}, "ptd_dbb:left_missile_attack": {"set_property": {"ptd_dbb:attack": "left_missile"}}, "ptd_dbb:left_shout_attack": {"set_property": {"ptd_dbb:attack": "left_shout"}}, "ptd_dbb:reset_attack": {"sequence": [{"set_property": {"ptd_dbb:attack": "none", "ptd_dbb:cooling_down": true}}, {"add": {"component_groups": ["ptd_dbb:cooldown"]}}]}, "ptd_dbb:cooldown_complete": {"sequence": [{"set_property": {"ptd_dbb:cooling_down": false}}, {"remove": {"component_groups": ["ptd_dbb:cooldown"]}}]}, "ptd_dbb:on_load": {"sequence": [{"set_property": {"ptd_dbb:attack": "none", "ptd_dbb:cooling_down": false}}, {"add": {"component_groups": ["ptd_dbb:default", "ptd_dbb:targeting", "ptd_dbb:melee"]}}]}}, "components": {"minecraft:type_family": {"family": ["void_hydra", "boss"]}, "minecraft:collision_box": {"width": 4, "height": 6}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 13.5, "height": 10.7, "pivot": [0, 5.35, 0]}]}, "minecraft:ground_offset": {"value": 8}, "minecraft:health": {"value": 1500, "max": 1500}, "minecraft:boss": {"hud_range": 32, "name": "Void Hydra", "should_darken_sky": false}, "minecraft:can_fly": {}, "minecraft:movement.hover": {}, "minecraft:behavior.melee_box_attack": {"priority": 2, "can_spread_on_fire": true, "speed_multiplier": 1, "horizontal_reach": 0, "cooldown_time": 999999999}, "minecraft:behavior.float": {"priority": 0}, "minecraft:knockback_resistance": {"value": 0.9}, "minecraft:follow_range": {"value": 256, "max": 256}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 10}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "is_family", "subject": "other", "value": "void_hydra"}}, "deals_damage": "no"}]}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:physics": {}, "minecraft:is_stackable": {}, "minecraft:floats_in_liquid": {}, "minecraft:persistent": {}, "minecraft:conditional_bandwidth_optimization": {}}}}