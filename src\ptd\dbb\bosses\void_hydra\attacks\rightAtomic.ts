import { Enti<PERSON>, <PERSON>ti<PERSON>D<PERSON>geCause, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
import { fixedLenRaycast } from "../../../utilities/raycasts";

/**
 * Attack timing constants for right atomic attack
 */
const DAMAGE_START_TIMING = 11; // Start continuous damage at tick 11
const DAMAGE_END_TIMING = 34; // End continuous damage at tick 34
const ANIMATION_TIME = 66; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes

/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
  /** Range of the atomic beam */
  RANGE: 38,
  /** Width of the beam */
  BEAM_WIDTH: 4,
  /** Total sweep angle in degrees */
  SWEEP_ANGLE: 90,
  /** Number of particles to spawn */
  PARTICLE_COUNT: 8
};

/**
 * Executes the right atomic attack for the Void Hydra
 * Creates a sweeping beam attack that continuously damages entities between ticks 11-34
 * The beam sweeps horizontally from left to right in a 90-degree arc
 *
 * @param voidHydra The void hydra entity
 */
export function executeRightAtomicAttack(voidHydra: Entity): void {
  let swipeInterval: number;

  // Start continuous swipe damage at tick 11
  let damageStartTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageStartTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic") {
        // Start continuous swipe effect
        let currentTick = DAMAGE_START_TIMING;
        swipeInterval = system.runInterval(() => {
          try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
            if (isDead || voidHydra.getProperty("ptd_dbb:attack") !== "right_atomic") {
              system.clearRun(swipeInterval);
              return;
            }

            if (currentTick <= DAMAGE_END_TIMING) {
              performAtomicBeam(voidHydra, currentTick);
              currentTick++;
            } else {
              system.clearRun(swipeInterval);
            }
          } catch (error) {
            system.clearRun(swipeInterval);
          }
        }, 1);
      }
    } catch (error) {
      system.clearRun(damageStartTiming);
    }
  }, DAMAGE_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (swipeInterval) system.clearRun(swipeInterval);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic") {
        voidHydra.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      voidHydra.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the atomic beam damage in a sweeping pattern
 * @param voidHydra The void hydra entity
 * @param currentTick The current tick in the attack sequence
 */
function performAtomicBeam(voidHydra: Entity, currentTick: number): void {
  try {
    const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
    if (!target) return;

    const viewDirection = voidHydra.getViewDirection();
    const origin: Vector3 = {
      x: voidHydra.location.x + (viewDirection.x * 12),
      y: voidHydra.location.y + 24,
      z: voidHydra.location.z + (viewDirection.z * 12)
    }; // Origin positioned 8 blocks above and 5 blocks in front of void hydra
    const damage = VOID_HYDRA_ATTACK_DAMAGES.right_atomic.damage;

    // Calculate target direction
    const targetDirection = {
      x: target.location.x - origin.x,
      y: target.location.y + 1 - origin.y,
      z: target.location.z - origin.z
    };

    // Normalize target direction
    const magnitude = Math.sqrt(targetDirection.x ** 2 + targetDirection.y ** 2 + targetDirection.z ** 2);
    if (magnitude === 0) return;

    targetDirection.x /= magnitude;
    targetDirection.y /= magnitude;
    targetDirection.z /= magnitude;

    // Calculate sweep progress (0 to 1)
    const totalTicks = DAMAGE_END_TIMING - DAMAGE_START_TIMING + 1;
    const tickProgress = (currentTick - DAMAGE_START_TIMING) / (totalTicks - 1);

    // Calculate sweep angle (-45 to +45 degrees for 90 degree total sweep)
    const sweepAngleRad = (tickProgress - 0.5) * ((ATTACK_CONFIG.SWEEP_ANGLE * Math.PI) / 180);

    // Calculate perpendicular vector for horizontal sweep
    const perpendicular = {
      x: -targetDirection.z,
      y: 0,
      z: targetDirection.x
    };

    // Calculate beam direction by rotating target direction by sweep angle
    const beamDirection = {
      x: targetDirection.x * Math.cos(sweepAngleRad) + perpendicular.x * Math.sin(sweepAngleRad),
      y: targetDirection.y,
      z: targetDirection.z * Math.cos(sweepAngleRad) + perpendicular.z * Math.sin(sweepAngleRad)
    };

    // Use raycast to get beam positions
    const beamPositions = fixedLenRaycast(origin, beamDirection, ATTACK_CONFIG.RANGE, 0.5);
    const entitiesHit = new Set<Entity>();

    // Process each position along the beam
    beamPositions.forEach((beamPos, index) => {
      // Create beam width by checking positions around the center
      for (let width = -ATTACK_CONFIG.BEAM_WIDTH; width <= ATTACK_CONFIG.BEAM_WIDTH; width++) {
        const widthPos = {
          x: beamPos.x + perpendicular.x * width,
          y: beamPos.y,
          z: beamPos.z + perpendicular.z * width
        };

        // Check for entities at this beam position
        voidHydra.dimension
          .getEntities({
            location: widthPos,
            maxDistance: 1.5,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["void_hydra"]
          })
          .forEach((entity) => {
            // Avoid hitting the same entity multiple times in one tick
            if (!entitiesHit.has(entity)) {
              entitiesHit.add(entity);

              entity.applyDamage(damage, {
                cause: EntityDamageCause.entityAttack,
                damagingEntity: voidHydra
              });

              // Apply knockback along beam direction
              try {
                entity.applyKnockback(beamDirection.x, beamDirection.z, 0.6, 0.2);
              } catch (knockbackError) {
                // Fallback to applyImpulse if applyKnockback fails
                try {
                  const impulse = {
                    x: beamDirection.x * 0.3,
                    y: 0.1,
                    z: beamDirection.z * 0.3
                  };
                  entity.applyImpulse(impulse);
                } catch (impulseError) {
                  // Ignore if both methods fail
                }
              }
            }
          });

        // Spawn particles for visual effect (reduce density)
        if (index % 4 === 0 && width === 0) {
          voidHydra.dimension.spawnParticle("minecraft:redstone_wire_dust_particle", widthPos);
        }
      }
    });

    // Spawn beam origin effect
    if (currentTick % 3 === 0) {
      voidHydra.dimension.spawnParticle("minecraft:large_explosion", origin);
    }
  } catch (error) {
    // Handle errors silently
  }
}
